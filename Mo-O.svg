<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="227.808875pt" height="178.597775pt" viewBox="0 0 227.808875 178.597775" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-07T15:32:28.274328</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.10.0, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 178.597775 
L 227.808875 178.597775 
L 227.808875 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 38.72375 138.0384 
L 213.93575 138.0384 
L 213.93575 7.2 
L 38.72375 7.2 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="me9099a5e11" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#me9099a5e11" x="38.72375" y="138.0384" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="38.72375" y="153.646525" transform="rotate(-0 38.72375 153.646525)">0</text>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#me9099a5e11" x="67.92575" y="138.0384" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="67.92575" y="153.646525" transform="rotate(-0 67.92575 153.646525)">2</text>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#me9099a5e11" x="97.12775" y="138.0384" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="97.12775" y="153.646525" transform="rotate(-0 97.12775 153.646525)">4</text>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#me9099a5e11" x="126.32975" y="138.0384" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="126.32975" y="153.646525" transform="rotate(-0 126.32975 153.646525)">6</text>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#me9099a5e11" x="155.53175" y="138.0384" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="155.53175" y="153.646525" transform="rotate(-0 155.53175 153.646525)">8</text>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <g>
       <use xlink:href="#me9099a5e11" x="184.73375" y="138.0384" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="184.73375" y="153.646525" transform="rotate(-0 184.73375 153.646525)">10</text>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_7">
      <g>
       <use xlink:href="#me9099a5e11" x="213.93575" y="138.0384" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="213.93575" y="153.646525" transform="rotate(-0 213.93575 153.646525)">12</text>
     </g>
    </g>
    <g id="text_8">
     <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="126.32975" y="168.896525" transform="rotate(-0 126.32975 168.896525)">Time (ps)</text>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_8">
      <defs>
       <path id="m2553bbb863" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m2553bbb863" x="38.72375" y="113.439086" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: end" x="31.72375" y="117.743148" transform="rotate(-0 31.72375 117.743148)">3</text>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_9">
      <g>
       <use xlink:href="#m2553bbb863" x="38.72375" y="85.633476" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: end" x="31.72375" y="89.937539" transform="rotate(-0 31.72375 89.937539)">4</text>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_10">
      <g>
       <use xlink:href="#m2553bbb863" x="38.72375" y="57.827867" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: end" x="31.72375" y="62.13193" transform="rotate(-0 31.72375 62.13193)">5</text>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_11">
      <g>
       <use xlink:href="#m2553bbb863" x="38.72375" y="30.022258" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: end" x="31.72375" y="34.32632" transform="rotate(-0 31.72375 34.32632)">6</text>
     </g>
    </g>
    <g id="text_13">
     <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="18.549375" y="72.6192" transform="rotate(-90 18.549375 72.6192)">Distance (Å)</text>
    </g>
   </g>
   <g id="line2d_12">
    <path d="M 38.72375 131.587863 
L 38.86976 132.0912 
L 38.942765 131.918305 
L 39.234785 129.052964 
L 39.74582 122.709587 
L 39.89183 121.397413 
L 40.402865 112.887284 
L 42.300995 67.961844 
L 44.491145 23.872407 
L 44.71016 22.146096 
L 45.221195 19.276029 
L 45.2942 19.259846 
L 45.73223 15.889111 
L 46.02425 13.928093 
L 46.243265 14.277526 
L 46.46228 13.1472 
L 46.7543 14.863696 
L 47.33834 26.842909 
L 47.63036 33.6443 
L 47.849375 36.078959 
L 48.06839 37.581101 
L 48.141395 37.412127 
L 48.2144 37.438709 
L 48.36041 37.888603 
L 48.433415 37.671664 
L 48.50642 38.067644 
L 48.79844 40.702837 
L 48.94445 41.570455 
L 49.017455 41.196136 
L 49.09046 37.674806 
L 49.163465 38.086023 
L 49.23647 38.245127 
L 49.38248 37.880345 
L 49.52849 38.22013 
L 49.6745 39.289895 
L 49.893515 40.733812 
L 50.11253 41.611274 
L 50.331545 43.946667 
L 50.69657 47.244218 
L 51.28061 51.13083 
L 51.791645 58.424631 
L 52.15667 60.87767 
L 52.229675 60.691177 
L 52.88672 55.304842 
L 52.959725 55.357339 
L 53.105735 57.099805 
L 53.32475 59.003349 
L 53.397755 59.046976 
L 53.543765 58.183 
L 53.76278 56.154359 
L 54.273815 47.828636 
L 54.419825 47.019131 
L 54.857855 42.956843 
L 55.149875 40.408793 
L 55.587905 42.089948 
L 55.66091 41.979587 
L 55.733915 42.210791 
L 56.09894 46.881661 
L 56.39096 50.827137 
L 56.53697 51.630469 
L 56.755985 51.667701 
L 57.048005 50.251756 
L 57.340025 48.829416 
L 57.632045 47.216412 
L 57.70505 47.362225 
L 57.778055 47.18502 
L 57.85106 47.242772 
L 57.924065 47.163582 
L 58.070075 46.276861 
L 58.362095 44.714547 
L 58.72712 42.697194 
L 58.87313 42.830995 
L 59.16515 43.519601 
L 59.60318 42.157043 
L 59.822195 41.663076 
L 59.8952 41.793818 
L 60.33323 44.488042 
L 60.698255 43.62187 
L 61.136285 39.668135 
L 61.574315 35.861213 
L 61.866335 34.338717 
L 62.158355 32.015058 
L 62.450375 32.969597 
L 62.52338 32.746039 
L 62.596385 32.52112 
L 62.8154 31.225295 
L 62.888405 31.165235 
L 63.10742 29.126055 
L 63.39944 27.252986 
L 63.54545 27.269808 
L 63.69146 27.154137 
L 63.83747 28.070137 
L 64.494515 34.396414 
L 64.640525 35.272485 
L 65.078555 39.566199 
L 65.44358 40.040952 
L 65.662595 39.680397 
L 65.7356 39.52602 
L 65.954615 38.852541 
L 66.02762 38.936708 
L 66.246635 38.624229 
L 66.46565 38.705922 
L 66.684665 40.601069 
L 67.77974 54.116263 
L 68.07176 56.012355 
L 68.94782 59.821835 
L 69.09383 59.614266 
L 69.38585 57.726404 
L 69.896885 52.54102 
L 70.334915 47.908744 
L 70.69994 44.835029 
L 70.772945 44.521465 
L 70.918955 45.129546 
L 71.13797 45.753392 
L 71.356985 45.819125 
L 71.576 46.779113 
L 71.649005 46.696308 
L 71.795015 46.289373 
L 72.01403 44.788204 
L 72.30605 41.537673 
L 72.45206 40.617863 
L 72.59807 40.678257 
L 72.89009 42.265818 
L 73.32812 46.174925 
L 73.401125 46.10505 
L 73.62014 44.859192 
L 74.131175 35.990593 
L 74.4962 29.539052 
L 74.64221 28.28285 
L 74.715215 28.340129 
L 74.93423 27.729796 
L 75.37226 30.725851 
L 75.445265 30.554012 
L 75.66428 29.464171 
L 75.9563 27.807318 
L 76.24832 26.961416 
L 76.39433 27.481742 
L 76.613345 29.816301 
L 77.051375 34.498071 
L 77.635415 39.709954 
L 77.781425 39.482504 
L 78.073445 41.754334 
L 78.365465 44.48604 
L 78.511475 45.071043 
L 78.58448 44.897424 
L 78.8765 42.289564 
L 79.16852 39.005666 
L 79.241525 39.000772 
L 80.263595 46.095985 
L 80.409605 46.059393 
L 80.77463 49.081724 
L 81.21266 53.623575 
L 81.431675 54.643708 
L 81.50468 54.540966 
L 81.7967 53.701765 
L 82.81877 48.935717 
L 83.11079 47.115645 
L 83.329805 45.629268 
L 83.84084 41.840531 
L 84.13286 40.921 
L 84.205865 41.005279 
L 84.643895 42.855714 
L 84.86291 43.671002 
L 85.30094 41.901314 
L 85.44695 41.615945 
L 85.665965 40.315116 
L 86.103995 41.436405 
L 86.177 41.137467 
L 86.250005 41.263148 
L 86.396015 42.201448 
L 86.46902 42.035449 
L 86.834045 40.497187 
L 86.90705 40.634852 
L 87.05306 41.096314 
L 87.19907 41.003082 
L 87.272075 41.148756 
L 87.49109 40.271572 
L 87.6371 39.097508 
L 87.856115 37.339248 
L 88.294145 34.409176 
L 88.65917 39.183594 
L 89.024195 43.186796 
L 89.0972 43.32713 
L 89.170205 43.017793 
L 89.24321 43.27911 
L 89.316215 43.124372 
L 89.462225 41.779748 
L 89.53523 41.871006 
L 89.608235 41.997855 
L 89.754245 41.875233 
L 89.900255 42.917331 
L 89.97326 42.793318 
L 90.26528 40.707647 
L 91.068335 32.029572 
L 91.14134 32.120719 
L 91.28735 33.039027 
L 91.506365 35.161179 
L 91.652375 35.921913 
L 92.0174 38.325958 
L 92.236415 36.845142 
L 92.45543 35.284135 
L 92.528435 35.387461 
L 92.820455 37.456226 
L 93.33149 43.961793 
L 93.62351 47.013821 
L 94.06154 48.047772 
L 94.280555 47.626462 
L 94.49957 48.091177 
L 94.572575 48.204651 
L 94.64558 48.153239 
L 95.010605 46.868203 
L 95.37563 47.953706 
L 95.66765 49.660748 
L 95.95967 52.998144 
L 96.3977 58.364543 
L 96.54371 59.088601 
L 96.762725 59.515278 
L 96.908735 59.785938 
L 97.27376 58.015332 
L 97.930805 54.290159 
L 98.222825 51.808092 
L 98.29583 51.862507 
L 98.806865 55.551311 
L 98.952875 55.907612 
L 99.17189 53.905302 
L 99.390905 51.813291 
L 99.46391 51.869431 
L 99.682925 52.694534 
L 99.974945 54.059095 
L 100.412975 56.922294 
L 100.48598 56.887009 
L 100.63199 56.724846 
L 100.851005 57.502402 
L 100.997015 56.619435 
L 101.21603 54.916258 
L 101.289035 55.032402 
L 101.65406 56.793943 
L 101.727065 56.825002 
L 101.80007 57.008019 
L 102.82214 63.154226 
L 103.041155 65.076233 
L 103.40618 67.727498 
L 103.55219 68.265759 
L 104.209235 75.737571 
L 104.57426 80.564041 
L 104.647265 80.743554 
L 104.793275 79.91503 
L 105.523325 68.549098 
L 105.815345 64.50822 
L 106.18037 62.744399 
L 106.47239 62.263529 
L 106.6184 61.260442 
L 106.983425 58.380726 
L 107.20244 57.497536 
L 107.34845 57.033961 
L 107.64047 57.235163 
L 107.78648 56.985858 
L 108.0785 54.502983 
L 109.97663 27.453353 
L 110.049635 27.426771 
L 110.56067 23.266468 
L 110.633675 23.451625 
L 110.70668 23.219587 
L 110.85269 22.39123 
L 110.9987 22.846464 
L 111.071705 22.523391 
L 111.14471 22.573552 
L 111.29072 22.832478 
L 111.509735 21.223089 
L 111.58274 21.447703 
L 111.655745 21.170064 
L 111.801755 21.986881 
L 111.947765 23.279286 
L 112.093775 23.874159 
L 112.531805 27.588516 
L 112.677815 27.31135 
L 112.75082 27.415815 
L 112.89683 28.070471 
L 113.553875 40.952059 
L 113.77289 43.265068 
L 113.845895 43.246188 
L 114.21092 44.187937 
L 114.721955 46.67754 
L 114.867965 47.109861 
L 115.013975 46.416167 
L 115.08698 46.551886 
L 115.159985 46.67462 
L 115.23299 46.518019 
L 115.52501 44.367005 
L 116.036045 37.234032 
L 116.182055 36.520317 
L 116.40107 37.096644 
L 116.474075 36.84036 
L 116.620085 37.069728 
L 116.766095 36.415824 
L 116.8391 36.611965 
L 116.98511 36.595726 
L 117.788165 30.344219 
L 118.226195 29.712781 
L 118.372205 29.47813 
L 118.518215 30.026706 
L 119.102255 33.831042 
L 119.32127 34.732806 
L 119.394275 34.824425 
L 119.61329 34.093277 
L 119.686295 34.252381 
L 119.90531 34.461145 
L 120.05132 35.115633 
L 120.124325 35.061746 
L 120.34334 35.351675 
L 120.708365 32.958419 
L 121.07339 29.033295 
L 121.146395 29.130726 
L 121.438415 30.910063 
L 121.51142 30.958083 
L 121.730435 32.057434 
L 122.09546 34.498683 
L 122.314475 33.375364 
L 122.38748 33.394077 
L 122.898515 34.726967 
L 123.044525 34.883067 
L 123.11753 35.115689 
L 123.920585 43.25403 
L 123.99359 43.186545 
L 124.1396 43.233064 
L 124.28561 42.735399 
L 124.72364 45.187048 
L 124.796645 44.984484 
L 125.5997 38.708035 
L 126.110735 30.688091 
L 126.256745 31.061603 
L 126.32975 30.996928 
L 126.402755 30.655642 
L 127.20581 33.70486 
L 127.278815 33.434423 
L 127.35182 33.589745 
L 127.570835 33.866272 
L 127.64384 34.102063 
L 127.716845 34.06244 
L 128.5199 29.889764 
L 128.66591 31.065969 
L 128.95793 34.096502 
L 129.10394 34.282855 
L 129.24995 34.245151 
L 129.322955 34.243205 
L 129.468965 33.268062 
L 129.760985 30.96281 
L 129.83399 31.063077 
L 130.12601 32.441151 
L 130.345025 32.877588 
L 131.075075 36.327068 
L 131.221085 36.457171 
L 131.4401 35.958199 
L 131.513105 36.032357 
L 131.87813 37.055825 
L 132.243155 38.214096 
L 132.31616 38.036057 
L 132.389165 38.111326 
L 132.681185 39.724969 
L 132.827195 40.525993 
L 132.9002 40.3888 
L 133.04621 40.449695 
L 133.265225 41.202671 
L 133.411235 41.158015 
L 134.141285 36.765257 
L 134.287295 37.219183 
L 134.50631 38.15551 
L 134.579315 38.242152 
L 134.79833 38.748631 
L 134.94434 39.397531 
L 135.455375 42.636884 
L 135.893405 44.6068 
L 135.96641 44.556389 
L 136.11242 43.888887 
L 136.25843 44.017683 
L 136.55045 42.946388 
L 136.915475 38.530857 
L 137.353505 36.559495 
L 137.42651 36.655147 
L 137.57252 36.271374 
L 137.645525 36.334437 
L 137.791535 36.61747 
L 137.86454 36.546093 
L 138.083555 37.302962 
L 138.15656 37.119917 
L 138.229565 37.181145 
L 138.375575 36.943685 
L 138.521585 36.344947 
L 138.667595 36.232613 
L 139.61666 29.462225 
L 139.835675 30.541305 
L 140.711735 37.39878 
L 140.857745 37.771764 
L 141.003755 36.843279 
L 141.587795 33.76759 
L 141.733805 35.402003 
L 142.09883 44.247079 
L 142.53686 52.093739 
L 142.755875 53.739747 
L 142.97489 54.107115 
L 143.193905 53.193256 
L 143.339915 53.422597 
L 143.41292 53.180354 
L 143.55893 53.881278 
L 143.923955 56.275814 
L 144.14297 57.403331 
L 144.507995 59.537773 
L 144.654005 59.567581 
L 144.87302 58.93378 
L 145.16504 58.876778 
L 145.384055 59.186783 
L 145.45706 59.177607 
L 145.822085 60.009662 
L 146.114105 58.485637 
L 146.552135 55.692591 
L 146.552135 55.692591 
" clip-path="url(#pa8c9d68687)" style="fill: none; stroke: #000000; stroke-linecap: square"/>
   </g>
   <g id="patch_3">
    <path d="M 38.72375 138.0384 
L 38.72375 7.2 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 213.93575 138.0384 
L 213.93575 7.2 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 38.72375 138.0384 
L 213.93575 138.0384 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 38.72375 7.2 
L 213.93575 7.2 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pa8c9d68687">
   <rect x="38.72375" y="7.2" width="175.212" height="130.8384"/>
  </clipPath>
 </defs>
</svg>
