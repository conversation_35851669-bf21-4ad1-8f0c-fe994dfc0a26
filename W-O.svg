<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="228.344906pt" height="182.837569pt" viewBox="0 0 228.344906 182.837569" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2023-11-30T12:51:21.645424</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.8.0, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 182.837569 
L 228.344906 182.837569 
L 228.344906 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 38.72375 142.278194 
L 214.471781 142.278194 
L 214.471781 11.317564 
L 38.72375 11.317564 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="m54230645ce" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m54230645ce" x="38.72375" y="142.278194" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <text style="font: 12px 'Helvetica', sans-serif; text-anchor: middle" x="38.72375" y="157.886319" transform="rotate(-0 38.72375 157.886319)">0</text>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#m54230645ce" x="68.015089" y="142.278194" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <text style="font: 12px 'Helvetica', sans-serif; text-anchor: middle" x="68.015089" y="157.886319" transform="rotate(-0 68.015089 157.886319)">2</text>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#m54230645ce" x="97.306427" y="142.278194" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <text style="font: 12px 'Helvetica', sans-serif; text-anchor: middle" x="97.306427" y="157.886319" transform="rotate(-0 97.306427 157.886319)">4</text>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#m54230645ce" x="126.597766" y="142.278194" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <text style="font: 12px 'Helvetica', sans-serif; text-anchor: middle" x="126.597766" y="157.886319" transform="rotate(-0 126.597766 157.886319)">6</text>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#m54230645ce" x="155.889104" y="142.278194" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <text style="font: 12px 'Helvetica', sans-serif; text-anchor: middle" x="155.889104" y="157.886319" transform="rotate(-0 155.889104 157.886319)">8</text>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <g>
       <use xlink:href="#m54230645ce" x="185.180443" y="142.278194" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <text style="font: 12px 'Helvetica', sans-serif; text-anchor: middle" x="185.180443" y="157.886319" transform="rotate(-0 185.180443 157.886319)">10</text>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_7">
      <g>
       <use xlink:href="#m54230645ce" x="214.471781" y="142.278194" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <text style="font: 12px 'Helvetica', sans-serif; text-anchor: middle" x="214.471781" y="157.886319" transform="rotate(-0 214.471781 157.886319)">12</text>
     </g>
    </g>
    <g id="text_8">
     <text style="font: 12px 'Helvetica', sans-serif; text-anchor: middle" x="126.597766" y="173.136319" transform="rotate(-0 126.597766 173.136319)">Time (ps)</text>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_8">
      <defs>
       <path id="mcbf5bc6d9f" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mcbf5bc6d9f" x="38.72375" y="113.728769" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <text style="font: 12px 'Helvetica', sans-serif; text-anchor: end" x="31.72375" y="118.032831" transform="rotate(-0 31.72375 118.032831)">3</text>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_9">
      <g>
       <use xlink:href="#mcbf5bc6d9f" x="38.72375" y="79.653867" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <text style="font: 12px 'Helvetica', sans-serif; text-anchor: end" x="31.72375" y="83.957929" transform="rotate(-0 31.72375 83.957929)">4</text>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_10">
      <g>
       <use xlink:href="#mcbf5bc6d9f" x="38.72375" y="45.578965" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <text style="font: 12px 'Helvetica', sans-serif; text-anchor: end" x="31.72375" y="49.883027" transform="rotate(-0 31.72375 49.883027)">5</text>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_11">
      <g>
       <use xlink:href="#mcbf5bc6d9f" x="38.72375" y="11.504063" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <text style="font: 12px 'Helvetica', sans-serif; text-anchor: end" x="31.72375" y="15.808125" transform="rotate(-0 31.72375 15.808125)">6</text>
     </g>
    </g>
    <g id="text_13">
     <text style="font: 12px 'Helvetica', sans-serif; text-anchor: middle" x="18.549375" y="76.797879" transform="rotate(-90 18.549375 76.797879)">Distance (Å)</text>
    </g>
   </g>
   <g id="line2d_12">
    <path d="M 38.72375 135.969525 
L 38.870207 136.325438 
L 38.943435 136.100543 
L 39.748947 129.437503 
L 39.822175 129.518499 
L 40.261545 126.165699 
L 40.48123 123.148809 
L 41.726112 100.540112 
L 42.238711 92.215613 
L 43.483593 60.559416 
L 43.849734 56.792981 
L 43.996191 55.776833 
L 44.069419 55.805933 
L 44.289104 56.163856 
L 44.435561 55.860283 
L 44.582018 54.771215 
L 45.167844 48.948734 
L 45.38753 49.654834 
L 45.460758 49.608935 
L 45.533986 49.861566 
L 45.753671 48.550671 
L 45.973356 47.248464 
L 46.193041 47.591122 
L 46.925325 40.910601 
L 47.218238 39.73546 
L 47.291467 39.873838 
L 47.364695 39.705474 
L 47.511152 40.088884 
L 48.682805 49.45386 
L 48.756033 49.557243 
L 48.829262 49.485822 
L 48.975719 49.369695 
L 49.195404 49.460403 
L 49.634774 52.930693 
L 50.000915 57.651021 
L 50.2206 58.257588 
L 50.806427 59.613054 
L 51.026112 61.014452 
L 51.392254 63.849211 
L 52.051309 72.69526 
L 52.270994 72.741602 
L 52.344222 72.683062 
L 52.710364 71.638053 
L 52.930049 71.223702 
L 53.589104 68.127997 
L 53.808789 67.75072 
L 54.028474 66.669489 
L 54.248159 65.363466 
L 54.321388 65.519802 
L 54.394616 65.443235 
L 54.614301 64.987415 
L 54.68753 65.040538 
L 55.1269 62.525402 
L 55.200128 62.425664 
L 55.273356 62.641052 
L 55.56627 61.693088 
L 55.785955 59.864084 
L 55.859183 59.908211 
L 55.932411 59.952201 
L 56.00564 59.601332 
L 56.078868 59.698105 
L 56.152096 59.799648 
L 56.371781 60.429488 
L 56.518238 61.06185 
L 56.737923 62.909562 
L 57.32375 67.864871 
L 57.76312 66.995347 
L 58.129262 65.199668 
L 58.275719 65.812744 
L 58.495404 68.034495 
L 58.788317 70.695813 
L 59.374144 74.122317 
L 59.447372 74.349904 
L 59.5206 74.270305 
L 59.667057 73.725754 
L 59.813514 72.712605 
L 60.326112 65.063675 
L 60.838711 58.443501 
L 60.985167 58.288494 
L 61.131624 58.312858 
L 61.497766 60.099643 
L 61.863907 61.941971 
L 61.937136 61.864383 
L 62.230049 60.50125 
L 62.596191 56.867809 
L 63.035561 52.467649 
L 63.182018 52.471499 
L 63.255246 52.399704 
L 63.401703 52.554301 
L 63.621388 51.945281 
L 63.841073 53.283811 
L 64.353671 58.619941 
L 64.646585 59.26089 
L 64.86627 61.255941 
L 65.30564 65.588463 
L 65.452096 66.150971 
L 65.598553 65.710553 
L 65.74501 65.4891 
L 65.964695 66.270335 
L 66.257608 67.970435 
L 66.843435 71.622753 
L 67.50249 76.998682 
L 67.575719 77.038107 
L 67.94186 76.404075 
L 68.308002 73.656956 
L 68.527687 70.688828 
L 68.747372 68.576866 
L 69.186742 66.364553 
L 69.626112 61.826389 
L 69.772569 62.489964 
L 70.358396 69.046043 
L 70.797766 76.826604 
L 71.017451 78.291791 
L 71.310364 81.642785 
L 71.383593 81.891225 
L 71.676506 80.143114 
L 72.335561 75.478328 
L 72.701703 71.83964 
L 72.994616 69.229945 
L 73.067844 68.963922 
L 73.214301 69.759878 
L 74.093041 77.487078 
L 74.16627 77.457126 
L 74.312726 76.388673 
L 74.752096 72.269835 
L 75.118238 68.949884 
L 75.630837 62.668584 
L 75.777293 62.149044 
L 75.996978 62.896034 
L 76.509577 66.684039 
L 76.582805 66.616536 
L 76.656033 66.701656 
L 76.80249 66.149199 
L 77.168632 62.470064 
L 77.24186 62.626945 
L 77.461545 64.091177 
L 78.193829 69.885819 
L 78.486742 71.101168 
L 78.706427 70.528506 
L 78.779656 70.585138 
L 78.926112 71.587928 
L 79.878081 82.623017 
L 80.024537 83.294191 
L 80.097766 83.320974 
L 80.317451 82.955759 
L 80.390679 82.987823 
L 80.610364 83.490087 
L 81.049734 86.538871 
L 81.342648 93.689182 
L 81.635561 99.566251 
L 81.708789 99.717135 
L 82.074931 95.907118 
L 83.832411 72.677542 
L 84.125325 69.47399 
L 84.271781 69.028903 
L 84.418238 69.534711 
L 84.564695 71.468053 
L 85.443435 90.394753 
L 85.589892 91.278792 
L 86.029262 90.40058 
L 86.248947 89.134833 
L 86.322175 89.151803 
L 86.468632 89.909901 
L 86.834774 94.92099 
L 86.908002 94.940379 
L 86.98123 94.783566 
L 87.4206 88.34525 
L 87.713514 80.987149 
L 88.079656 75.214827 
L 88.519026 71.960469 
L 88.592254 71.828327 
L 88.665482 71.912424 
L 88.738711 72.030255 
L 88.811939 71.856473 
L 89.031624 72.463415 
L 89.104852 72.032981 
L 89.178081 72.223016 
L 89.397766 73.384323 
L 90.056821 81.987077 
L 90.642648 93.24212 
L 90.935561 95.997996 
L 91.082018 96.295265 
L 91.374931 94.13093 
L 91.88753 84.187123 
L 91.960758 84.074813 
L 92.253671 82.573234 
L 92.400128 81.731107 
L 92.473356 81.800006 
L 92.546585 81.922301 
L 92.693041 81.63355 
L 92.839498 82.145083 
L 92.912726 82.137348 
L 93.132411 80.586463 
L 93.498553 77.879143 
L 94.157608 72.60227 
L 94.230837 72.874903 
L 94.450522 75.201435 
L 94.889892 80.366645 
L 95.109577 81.728585 
L 95.182805 81.797451 
L 95.329262 81.300979 
L 95.475719 80.829791 
L 95.548947 80.840287 
L 95.622175 80.719661 
L 95.84186 81.245199 
L 95.915089 81.110364 
L 95.988317 81.031788 
L 96.208002 79.909088 
L 96.427687 78.610698 
L 96.574144 78.677859 
L 97.526112 86.067581 
L 98.331624 94.227941 
L 98.404852 94.132258 
L 98.478081 94.305086 
L 98.551309 94.234824 
L 98.697766 92.794853 
L 99.137136 87.586674 
L 99.356821 85.588386 
L 99.796191 80.053702 
L 99.942648 79.570111 
L 100.089104 80.521107 
L 100.674931 88.158792 
L 101.18753 92.948428 
L 101.260758 92.95463 
L 101.333986 92.649694 
L 101.993041 84.513629 
L 102.432411 79.178897 
L 102.578868 78.618876 
L 102.725325 77.941773 
L 102.871781 78.032106 
L 102.94501 77.742844 
L 103.091467 78.56272 
L 103.38438 80.446415 
L 103.457608 80.662518 
L 103.604065 80.261013 
L 103.896978 80.94275 
L 104.116663 82.200284 
L 104.189892 82.366842 
L 104.922175 88.674345 
L 105.434774 85.234552 
L 106.093829 87.576554 
L 106.45997 86.444722 
L 106.679656 85.568622 
L 107.045797 83.486475 
L 107.265482 81.522807 
L 107.338711 81.410973 
L 108.583593 69.67987 
L 108.730049 69.198017 
L 108.876506 68.511476 
L 109.022963 69.153379 
L 109.242648 70.034351 
L 109.315876 69.962283 
L 109.462333 68.948044 
L 109.535561 69.216077 
L 109.682018 69.537744 
L 109.755246 69.131809 
L 109.828474 69.157809 
L 109.901703 69.35115 
L 110.048159 68.831746 
L 110.194616 68.536487 
L 110.560758 69.497092 
L 110.707215 69.247937 
L 110.853671 69.023962 
L 111.293041 65.795945 
L 111.36627 65.792537 
L 111.439498 65.990376 
L 112.098553 73.195276 
L 112.171781 73.669053 
L 112.24501 73.645678 
L 112.464695 73.180351 
L 112.68438 72.210852 
L 113.050522 73.853705 
L 113.343435 76.638579 
L 113.416663 77.050237 
L 113.489892 77.012176 
L 113.56312 76.840643 
L 113.636348 76.959257 
L 113.709577 76.907157 
L 113.782805 76.889302 
L 113.856033 76.694666 
L 114.075719 77.256152 
L 114.148947 77.360353 
L 114.44186 78.907592 
L 114.588317 79.62446 
L 114.661545 79.571882 
L 114.734774 79.57989 
L 114.808002 79.714486 
L 114.88123 79.649607 
L 115.174144 77.824215 
L 115.467057 72.992121 
L 115.979656 61.244833 
L 116.492254 57.788275 
L 116.858396 55.791588 
L 116.931624 56.103032 
L 117.297766 59.284026 
L 117.590679 61.254169 
L 117.663907 61.295741 
L 117.810364 60.309783 
L 118.396191 53.587078 
L 118.615876 56.556194 
L 118.908789 66.195677 
L 119.421388 81.665104 
L 119.714301 85.709386 
L 119.78753 85.902693 
L 119.860758 85.760396 
L 120.007215 84.498772 
L 120.446585 78.750814 
L 120.519813 78.73732 
L 120.812726 79.255804 
L 120.885955 79.279009 
L 120.959183 79.087201 
L 121.10564 77.631998 
L 121.471781 70.189699 
L 121.911152 62.643403 
L 122.130837 61.304021 
L 122.277293 61.541012 
L 122.716663 62.815277 
L 122.789892 62.71145 
L 122.86312 62.946261 
L 122.936348 62.853509 
L 123.082805 62.884892 
L 123.156033 62.912084 
L 123.229262 63.261556 
L 123.30249 63.024667 
L 123.375719 63.214839 
L 123.448947 63.081027 
L 123.522175 63.230207 
L 123.668632 62.75108 
L 123.888317 63.213408 
L 123.961545 62.996351 
L 124.254459 65.867434 
L 124.6206 71.550173 
L 124.767057 72.429783 
L 125.05997 70.21764 
L 125.792254 61.394217 
L 125.865482 61.257917 
L 126.304852 62.170614 
L 126.378081 62.094967 
L 126.890679 61.147004 
L 127.110364 59.904462 
L 127.330049 60.68965 
L 127.842648 64.161508 
L 127.915876 64.186485 
L 128.062333 64.711988 
L 128.135561 64.606594 
L 128.501703 63.07629 
L 128.794616 65.235344 
L 129.380443 71.544347 
L 129.600128 72.663673 
L 130.259183 71.066208 
L 130.478868 72.116669 
L 130.552096 72.032231 
L 130.771781 70.985416 
L 131.137923 66.324753 
L 131.504065 61.542204 
L 131.650522 61.327737 
L 132.016663 61.185883 
L 132.456033 62.802396 
L 132.529262 62.75326 
L 132.675719 62.093809 
L 132.968632 59.196181 
L 133.04186 59.374359 
L 133.188317 61.29465 
L 133.9206 77.237275 
L 133.993829 77.416611 
L 134.213514 76.837167 
L 134.286742 76.923377 
L 134.579656 76.214176 
L 134.799341 74.428037 
L 135.019026 74.799624 
L 135.165482 75.504532 
L 135.238711 75.433383 
L 135.385167 75.123574 
L 135.458396 75.319914 
L 135.531624 75.250265 
L 135.751309 75.355079 
L 136.044222 73.853092 
L 136.410364 69.054766 
L 136.849734 61.191301 
L 136.922963 61.189188 
L 137.069419 61.791019 
L 137.655246 67.490864 
L 137.801703 67.834373 
L 138.021388 68.917138 
L 138.241073 69.472524 
L 138.38753 69.305489 
L 138.680443 70.385459 
L 139.339498 77.608384 
L 139.632411 79.222376 
L 139.852096 80.543596 
L 139.998553 79.508299 
L 140.730837 68.740664 
L 141.756033 51.492391 
L 142.195404 45.165363 
L 142.634774 37.825732 
L 143.074144 34.909159 
L 143.147372 34.839918 
L 143.2206 34.959249 
L 143.806427 38.718665 
L 144.245797 42.833993 
L 144.465482 43.308247 
L 144.611939 43.873379 
L 144.758396 43.680618 
L 145.051309 44.107406 
L 145.344222 45.552795 
L 145.490679 45.987523 
L 145.563907 45.852859 
L 146.003278 43.042701 
L 146.149734 42.728054 
L 146.222963 42.325391 
L 146.296191 42.553454 
L 146.369419 42.43426 
L 146.735561 39.348266 
L 146.955246 37.991199 
L 147.028474 37.731719 
L 147.394616 34.944154 
L 147.614301 34.507518 
L 147.68753 34.395071 
L 147.760758 34.594784 
L 148.56627 42.201256 
L 148.639498 41.946784 
L 148.712726 42.047135 
L 148.932411 41.23036 
L 149.078868 41.698072 
L 149.152096 41.873353 
L 149.225325 41.634045 
L 149.298553 41.817436 
L 149.518238 41.544189 
L 149.957608 43.30501 
L 150.250522 42.007233 
L 150.543435 39.385408 
L 150.689892 38.805078 
L 150.76312 38.787973 
L 151.422175 44.681227 
L 151.568632 44.241797 
L 151.861545 40.509948 
L 152.447372 33.168408 
L 152.593829 33.084857 
L 152.95997 34.742532 
L 153.033199 34.668454 
L 153.106427 34.8743 
L 153.179656 34.657243 
L 153.252884 34.820053 
L 153.326112 34.698985 
L 153.399341 34.823324 
L 153.619026 33.988898 
L 153.692254 33.962422 
L 153.765482 33.746932 
L 153.838711 33.821011 
L 154.058396 33.856176 
L 154.351309 34.33745 
L 154.570994 33.589812 
L 154.644222 33.700658 
L 154.717451 33.326993 
L 154.790679 33.374766 
L 154.863907 33.197304 
L 155.156821 35.229701 
L 155.742648 41.074637 
L 155.815876 41.06036 
L 155.962333 40.42793 
L 156.328474 38.5028 
L 156.474931 38.831487 
L 156.767844 40.78374 
L 157.133986 42.987636 
L 157.353671 43.444615 
L 157.4269 43.22858 
L 157.500128 43.505711 
L 157.573356 43.187861 
L 158.012726 45.254878 
L 158.085955 45.799088 
L 158.159183 45.7797 
L 158.30564 46.049505 
L 158.74501 49.921709 
L 158.818238 49.840542 
L 158.891467 49.896834 
L 159.257608 48.527977 
L 159.330837 48.415905 
L 159.696978 46.549179 
L 159.770207 46.527133 
L 159.989892 47.609113 
L 160.282805 51.537609 
L 160.722175 57.297562 
L 160.94186 58.588149 
L 161.088317 58.361176 
L 161.600915 56.386195 
L 161.893829 53.854702 
L 161.967057 53.861585 
L 162.040285 53.265683 
L 162.113514 53.390261 
L 162.186742 53.027909 
L 162.25997 53.134086 
L 162.845797 50.93939 
L 163.065482 48.620388 
L 163.651309 42.602249 
L 163.724537 42.536587 
L 164.163907 44.9419 
L 164.896191 53.450131 
L 165.262333 58.797505 
L 165.482018 60.22388 
L 166.067844 65.336206 
L 166.28753 63.821202 
L 167.019813 57.137035 
L 167.312726 55.345752 
L 167.532411 57.059515 
L 167.752096 61.098379 
L 168.704065 84.688433 
L 168.92375 86.1634 
L 169.070207 85.930089 
L 169.582805 83.61688 
L 169.875719 81.766783 
L 169.948947 81.591229 
L 170.388317 83.122896 
L 170.68123 82.247001 
L 170.827687 82.269422 
L 171.340285 83.422994 
L 171.413514 83.45775 
L 172.219026 88.213005 
L 172.365482 87.208068 
L 172.951309 82.210847 
L 173.390679 83.685405 
L 173.463907 83.691368 
L 173.537136 83.369769 
L 173.756821 84.092838 
L 173.976506 83.710211 
L 174.196191 83.203074 
L 174.269419 83.358286 
L 174.415876 83.410045 
L 174.855246 85.049661 
L 175.148159 83.769433 
L 175.880443 75.873154 
L 176.0269 75.677428 
L 176.319813 76.530425 
L 176.46627 76.993775 
L 176.685955 79.550279 
L 177.271781 87.919518 
L 177.564695 88.850717 
L 177.930837 86.058313 
L 178.882805 71.709371 
L 179.10249 70.40846 
L 179.395404 71.032916 
L 179.54186 71.983129 
L 179.834774 75.708129 
L 180.567057 85.650879 
L 180.640285 85.020902 
L 180.85997 81.368686 
L 181.372569 71.569017 
L 182.104852 61.039157 
L 182.251309 61.126252 
L 182.324537 61.300341 
L 182.544222 61.016906 
L 182.837136 63.502363 
L 183.642648 73.833703 
L 184.228474 78.214066 
L 184.667844 82.14498 
L 184.741073 82.374032 
L 185.033986 79.615907 
L 185.546585 72.990758 
L 185.839498 70.094664 
L 186.278868 69.089931 
L 186.352096 69.127823 
L 186.498553 69.247426 
L 186.864695 67.620758 
L 187.230837 64.271911 
L 187.743435 62.521824 
L 187.816663 62.599685 
L 188.84186 66.51346 
L 189.061545 66.42139 
L 189.354459 65.392873 
L 189.647372 63.045453 
L 189.793829 62.877566 
L 189.940285 63.00981 
L 190.379656 64.451417 
L 190.452884 64.562331 
L 190.672569 63.445254 
L 191.258396 57.613061 
L 191.478081 56.209789 
L 191.624537 56.565599 
L 191.990679 58.608526 
L 192.283593 59.27762 
L 192.649734 61.107886 
L 192.722963 60.883196 
L 193.235561 58.253908 
L 194.114301 46.149174 
L 194.18753 46.174049 
L 194.260758 46.294469 
L 194.700128 49.785068 
L 195.871781 66.820475 
L 195.94501 67.072254 
L 196.091467 65.908051 
L 196.677293 59.881598 
L 196.896978 59.227224 
L 197.043435 59.667267 
L 197.409577 61.70297 
L 197.482805 61.618805 
L 197.629262 60.569877 
L 197.995404 54.181548 
L 198.58123 40.89295 
L 198.874144 36.88121 
L 199.093829 34.852049 
L 199.533199 30.436351 
L 199.606427 30.283797 
L 199.826112 29.540692 
L 200.338711 27.043172 
L 200.558396 25.752619 
L 200.631624 25.930797 
L 201.070994 22.368811 
L 201.730049 17.27032 
L 201.949734 19.347935 
L 202.755246 32.951794 
L 202.828474 33.539756 
L 202.828474 33.539756 
" clip-path="url(#p3b0bd848bd)" style="fill: none; stroke: #000000; stroke-linecap: square"/>
   </g>
   <g id="patch_3">
    <path d="M 38.72375 142.278194 
L 38.72375 11.317564 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 214.471781 142.278194 
L 214.471781 11.317564 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 38.72375 142.278194 
L 214.471781 142.278194 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 38.72375 11.317564 
L 214.471781 11.317564 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p3b0bd848bd">
   <rect x="38.72375" y="11.317564" width="175.748031" height="130.96063"/>
  </clipPath>
 </defs>
</svg>
