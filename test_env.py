#!/usr/bin/env python3
"""
测试Python环境和包导入
"""
import sys
print(f"Python executable: {sys.executable}")
print(f"Python version: {sys.version}")
print(f"Python path: {sys.path[:3]}...")  # 只显示前3个路径

try:
    import numpy as np
    print(f"✅ numpy imported successfully, version: {np.__version__}")
except ImportError as e:
    print(f"❌ numpy import failed: {e}")

try:
    import matplotlib.pyplot as plt
    print("✅ matplotlib imported successfully")
except ImportError as e:
    print(f"❌ matplotlib import failed: {e}")

print("Environment test completed!")
