#!/usr/bin/env python3
"""
Mo-O距离随时间变化的可视化脚本

Author: MengjiaHe <EMAIL>
Date: 2023-11-10 16:43:10
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2025-07-07 15:30:55
Description: 分析和可视化Mo-O键距离随分子动力学模拟时间的变化

Copyright (c) 2023 by MengjiaHe, All Rights Reserved.
"""

import numpy as np
import matplotlib.pyplot as plt


def load_distance_data(filename):
    """
    加载距离数据文件
    
    Args:
        filename (str): 数据文件名
        
    Returns:
        np.ndarray: 距离数据数组
    """
    try:
        data = np.loadtxt(filename)
        print(f"✅ 成功加载数据文件: {filename}")
        print(f"   数据点数量: {len(data)}")
        print(f"   距离范围: {data.min():.3f} - {data.max():.3f} Å")
        return data
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {filename}")
        raise
    except Exception as e:
        print(f"❌ 错误: 加载文件时出现问题 - {e}")
        raise


def create_time_axis(data_length, timestep_fs=10, output_interval=0.5):
    """
    创建时间轴
    
    Args:
        data_length (int): 数据点数量
        timestep_fs (float): 时间步长 (飞秒)
        output_interval (float): 输出间隔 (以时间步为单位)
        
    Returns:
        np.ndarray: 时间轴 (皮秒)
    """
    # 计算时间轴: 数据点索引 × 时间步长 × 输出间隔 / 1000 (fs -> ps)
    time_ps = np.arange(data_length) * timestep_fs * output_interval / 1000
    print(f"📊 时间轴信息:")
    print(f"   时间范围: 0 - {time_ps[-1]:.2f} ps")
    print(f"   时间步长: {timestep_fs} fs")
    print(f"   输出间隔: {output_interval}")
    return time_ps


def setup_plot_style():
    """
    设置绘图样式和参数
    """
    # 设置图形大小 (转换厘米到英寸)
    fig_width_cm, fig_height_cm = 8, 6
    fig_width_inch = fig_width_cm / 2.54
    fig_height_inch = fig_height_cm / 2.54
    
    plt.figure(figsize=(fig_width_inch, fig_height_inch))
    
    # 设置字体和样式
    plt.rcParams.update({
        'font.size': 12,
        'font.family': 'sans-serif',
        'font.sans-serif': 'Helvetica'
    })
    plt.rcParams['svg.fonttype'] = 'none'  # 确保SVG中的文字可编辑
    
    print("🎨 绘图样式设置完成")


def plot_distance_vs_time(time_data, distance_data, 
                         line_color='black', line_width=1, 
                         show_legend=False):
    """
    绘制距离随时间变化的图形
    
    Args:
        time_data (np.ndarray): 时间数据 (ps)
        distance_data (np.ndarray): 距离数据 (Å)
        line_color (str): 线条颜色
        line_width (float): 线条宽度
        show_legend (bool): 是否显示图例
    """
    plt.plot(time_data, distance_data, 
             color=line_color, 
             linewidth=line_width, 
             label='Mo-O Distance' if show_legend else None)
    
    print(f"📈 绘制完成: {len(time_data)} 个数据点")


def customize_axes(x_range=(0, 12), x_tick_interval=2):
    """
    自定义坐标轴
    
    Args:
        x_range (tuple): x轴范围
        x_tick_interval (float): x轴刻度间隔
    """
    # 设置坐标轴标签
    plt.xlabel('Time (ps)')
    plt.ylabel('Distance (Å)')
    
    # 设置x轴范围和刻度
    plt.xlim(x_range)
    x_ticks = np.arange(x_range[0], x_range[1] + 1, x_tick_interval)
    plt.xticks(x_ticks)
    
    print(f"📊 坐标轴设置: x范围 {x_range}, 刻度间隔 {x_tick_interval}")


def save_and_show_plot(output_filename='Mo-O_distance.svg', 
                      show_plot=True, show_grid=False, show_legend=False):
    """
    保存和显示图形
    
    Args:
        output_filename (str): 输出文件名
        show_plot (bool): 是否显示图形
        show_grid (bool): 是否显示网格
        show_legend (bool): 是否显示图例
    """
    # 可选设置
    if show_grid:
        plt.grid(True, alpha=0.3)
    
    if show_legend:
        plt.legend()
    
    # 保存图形
    plt.savefig(output_filename, bbox_inches='tight', format='svg', dpi=300)
    print(f"💾 图形已保存: {output_filename}")
    
    # 显示图形
    if show_plot:
        plt.show()
        print("👁️ 图形已显示")


def main():
    """
    主函数 - 执行完整的数据分析和可视化流程
    """
    print("🚀 开始Mo-O距离分析...")
    print("=" * 50)
    
    # 1. 加载数据
    distance_data = load_distance_data('combined_column2.dat')
    
    # 2. 创建时间轴
    time_data = create_time_axis(len(distance_data))
    
    # 3. 设置绘图样式
    setup_plot_style()
    
    # 4. 绘制图形
    plot_distance_vs_time(time_data, distance_data)
    
    # 5. 自定义坐标轴
    customize_axes()
    
    # 6. 保存和显示
    save_and_show_plot()
    
    print("=" * 50)
    print("✅ 分析完成!")


# 程序入口点
if __name__ == "__main__":
    main()
