{"python.defaultInterpreterPath": "/opt/miniconda3/envs/Plot.conda/bin/python", "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "python.condaPath": "/opt/miniconda3/bin/conda", "python.envFile": "${workspaceFolder}/.env", "terminal.integrated.env.osx": {"CONDA_DEFAULT_ENV": "Plot.conda", "CONDA_PREFIX": "/opt/miniconda3/envs/Plot.conda"}, "code-runner.executorMap": {"python": "/opt/miniconda3/envs/Plot.conda/bin/python"}, "code-runner.runInTerminal": true, "code-runner.preserveFocus": false, "code-runner.clearPreviousOutput": true}