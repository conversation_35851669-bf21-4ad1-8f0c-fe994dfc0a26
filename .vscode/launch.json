{"version": "0.2.0", "configurations": [{"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "python": "/opt/miniconda3/envs/Plot.conda/bin/python", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}", "CONDA_DEFAULT_ENV": "Plot.conda", "CONDA_PREFIX": "/opt/miniconda3/envs/Plot.conda"}}, {"name": "Python: Run with <PERSON><PERSON>", "type": "node-terminal", "request": "launch", "command": "./run_with_conda.sh ${file}", "cwd": "${workspaceFolder}"}]}