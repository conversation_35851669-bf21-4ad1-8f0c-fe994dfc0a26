'''
Author: MengjiaHe <EMAIL>
Date: 2023-11-10 16:43:10
LastEditors: MengjiaHe <EMAIL>
LastEditTime: 2025-07-07 15:22:33
FilePath: /undefined/Users/<USER>/Documents/My_Library/inp/hmj_inp/Fe_mineral/geothite_zyc/Mo-W-on-Geothite/cons-W/v1-H0/mono/v3-5cn/free/dis_oh_20231127.py
Description: 

Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
'''
import numpy as np
import matplotlib.pyplot as plt

# Load data
F = np.loadtxt('Mo-O.dat')
# E = np.loadtxt('2.dat')

# 创建时间轴（假设每个数据点对应一个时间步）
time_steps = np.arange(len(F)) * 10 * 0.5 / 1000  # 转换为ps

# 创建一个 8x6 厘米大小的图
plt.figure(figsize=(8/2.54, 6/2.54))
plt.rcParams.update({'font.size': 12, 'font.family': 'sans-serif', 'font.sans-serif': 'Helvetica'})
plt.rcParams['svg.fonttype'] = 'none'

# 绘制图形
# plt.plot(E[:, 0] * 10 * 0.5 / 1000, E[:, 1], 'r', linewidth=1, label='E Data')
plt.plot(time_steps, F, 'k', linewidth=1, label='F Data')

# 设置坐标轴标签
Ang = 'Å'
plt.xlabel('Time (ps)')
plt.ylabel(f'Distance ({Ang})')

# 设置 x 轴范围为 0 到 8，设置 x 轴刻度间隔为 2
x_range=(0, 12)
plt.xlim(x_range)
plt.xticks(np.arange(x_range[0], x_range[1] + 1, 2))

# plt.ylim(2, 8)
# plt.xticks(np.arange(2, 9, 2))  # 设置刻度为 2 到 8，间隔为 2

# 

# 设置图例
# plt.legend()

# 显示网格
# plt.grid(True)

# 保存图形
plt.savefig('Mo-O.svg', bbox_inches='tight', format='svg')

# 显示图形
plt.show()
