<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="227.808875pt" height="178.597775pt" viewBox="0 0 227.808875 178.597775" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-07-11T14:02:38.645548</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.10.0, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 178.597775 
L 227.808875 178.597775 
L 227.808875 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 38.72375 138.0384 
L 213.93575 138.0384 
L 213.93575 7.2 
L 38.72375 7.2 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="m423827a51e" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m423827a51e" x="38.72375" y="138.0384" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="38.72375" y="153.646525" transform="rotate(-0 38.72375 153.646525)">0</text>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#m423827a51e" x="67.92575" y="138.0384" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="67.92575" y="153.646525" transform="rotate(-0 67.92575 153.646525)">2</text>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#m423827a51e" x="97.12775" y="138.0384" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="97.12775" y="153.646525" transform="rotate(-0 97.12775 153.646525)">4</text>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#m423827a51e" x="126.32975" y="138.0384" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="126.32975" y="153.646525" transform="rotate(-0 126.32975 153.646525)">6</text>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#m423827a51e" x="155.53175" y="138.0384" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="155.53175" y="153.646525" transform="rotate(-0 155.53175 153.646525)">8</text>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <g>
       <use xlink:href="#m423827a51e" x="184.73375" y="138.0384" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="184.73375" y="153.646525" transform="rotate(-0 184.73375 153.646525)">10</text>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_7">
      <g>
       <use xlink:href="#m423827a51e" x="213.93575" y="138.0384" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="213.93575" y="153.646525" transform="rotate(-0 213.93575 153.646525)">12</text>
     </g>
    </g>
    <g id="text_8">
     <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="126.32975" y="168.896525" transform="rotate(-0 126.32975 168.896525)">Time (ps)</text>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_8">
      <defs>
       <path id="m048cd2ac56" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m048cd2ac56" x="38.72375" y="113.439086" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: end" x="31.72375" y="117.743148" transform="rotate(-0 31.72375 117.743148)">3</text>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_9">
      <g>
       <use xlink:href="#m048cd2ac56" x="38.72375" y="85.633476" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: end" x="31.72375" y="89.937539" transform="rotate(-0 31.72375 89.937539)">4</text>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_10">
      <g>
       <use xlink:href="#m048cd2ac56" x="38.72375" y="57.827867" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: end" x="31.72375" y="62.13193" transform="rotate(-0 31.72375 62.13193)">5</text>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_11">
      <g>
       <use xlink:href="#m048cd2ac56" x="38.72375" y="30.022258" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: end" x="31.72375" y="34.32632" transform="rotate(-0 31.72375 34.32632)">6</text>
     </g>
    </g>
    <g id="text_13">
     <text style="font-size: 12px; font-family: 'Helvetica', sans-serif; text-anchor: middle" x="18.549375" y="72.6192" transform="rotate(-90 18.549375 72.6192)">Distance (Å)</text>
    </g>
   </g>
   <g id="line2d_12">
    <path d="M 38.72375 131.587863 
L 38.86976 132.0912 
L 38.942765 131.918305 
L 39.234785 129.052964 
L 39.74582 122.709587 
L 39.89183 121.397413 
L 40.402865 112.887284 
L 42.300995 67.961844 
L 44.491145 23.872407 
L 44.71016 22.146096 
L 45.221195 19.276029 
L 45.2942 19.259846 
L 45.73223 15.889111 
L 46.02425 13.928093 
L 46.243265 14.277526 
L 46.46228 13.1472 
L 46.7543 14.863696 
L 47.33834 26.842909 
L 47.63036 33.6443 
L 47.849375 36.078959 
L 48.06839 37.581101 
L 48.141395 37.412127 
L 48.2144 37.438709 
L 48.36041 37.888603 
L 48.433415 37.671664 
L 48.50642 38.067644 
L 48.79844 40.702837 
L 48.94445 41.570455 
L 49.09046 41.119004 
L 49.163465 41.310028 
L 49.23647 41.167358 
L 49.52849 39.191241 
L 49.6745 38.429145 
L 49.82051 38.874006 
L 49.893515 38.676753 
L 49.96652 38.771015 
L 50.11253 39.321705 
L 50.25854 38.95898 
L 50.331545 39.114553 
L 50.477555 38.676531 
L 50.55056 38.821927 
L 50.69657 39.687654 
L 50.769575 39.662101 
L 50.98859 38.700694 
L 51.645635 31.434671 
L 51.937655 30.253294 
L 52.01066 30.534492 
L 52.959725 41.858077 
L 53.17874 42.65657 
L 53.251745 42.735844 
L 53.61677 40.189323 
L 53.835785 38.885157 
L 54.127805 37.64169 
L 54.34682 36.098617 
L 54.419825 36.098395 
L 54.63884 34.077455 
L 54.93086 31.19849 
L 55.003865 31.283409 
L 55.07687 31.157116 
L 55.295885 31.814941 
L 55.441895 31.354174 
L 55.587905 32.155532 
L 55.80692 35.022512 
L 56.171945 39.290785 
L 56.609975 43.822654 
L 56.82899 43.525412 
L 56.975 43.608301 
L 57.41303 45.561422 
L 57.778055 45.217968 
L 57.924065 45.530725 
L 58.946135 51.930269 
L 59.01914 51.842988 
L 59.384165 53.471979 
L 59.74919 54.451515 
L 59.8952 54.235855 
L 60.04121 54.178464 
L 60.18722 53.641482 
L 60.33323 53.63289 
L 60.47924 53.355307 
L 61.136285 55.480156 
L 61.50131 56.283877 
L 61.64732 56.359091 
L 62.304365 58.489724 
L 62.450375 58.759327 
L 62.596385 60.012526 
L 62.8154 61.251294 
L 63.180425 62.35576 
L 63.618455 65.20311 
L 64.2755 74.053247 
L 65.00555 79.532008 
L 65.29757 80.438666 
L 65.44358 80.453792 
L 65.662595 80.314402 
L 65.7356 80.27464 
L 65.88161 80.374796 
L 66.100625 79.534594 
L 66.246635 79.694198 
L 66.392645 80.875242 
L 66.61166 82.786488 
L 66.684665 82.89999 
L 66.830675 82.400908 
L 67.414715 78.108194 
L 67.706735 75.770215 
L 68.144765 73.244381 
L 68.21777 73.376597 
L 68.290775 73.248218 
L 68.36378 73.376291 
L 68.80181 76.172924 
L 68.94782 76.664332 
L 69.67787 82.020972 
L 69.82388 81.942143 
L 70.042895 82.816685 
L 70.188905 83.953517 
L 70.69994 92.011333 
L 71.064965 97.16716 
L 71.210975 97.514535 
L 71.42999 97.187041 
L 71.576 96.480667 
L 71.86802 93.305906 
L 73.255115 77.738658 
L 73.401125 77.086894 
L 73.62014 77.042794 
L 73.693145 77.056113 
L 73.839155 76.523163 
L 74.131175 74.814564 
L 74.423195 73.208206 
L 74.715215 68.972188 
L 75.08024 64.962397 
L 75.37226 63.715371 
L 75.591275 63.377672 
L 75.66428 63.360154 
L 75.883295 64.089218 
L 75.9563 63.898026 
L 76.175315 62.603008 
L 76.83236 57.626999 
L 77.12438 57.311851 
L 77.197385 57.153609 
L 77.4164 58.377334 
L 78.29246 64.339746 
L 78.43847 64.20589 
L 78.73049 63.503687 
L 78.949505 64.058909 
L 79.31453 62.259887 
L 79.46054 62.362044 
L 79.533545 62.605149 
L 79.60655 62.568028 
L 79.825565 63.117912 
L 80.263595 65.485393 
L 80.3366 65.464122 
L 80.555615 64.764727 
L 81.35867 62.171242 
L 81.577685 62.678389 
L 82.015715 62.075007 
L 82.23473 62.356372 
L 82.453745 62.012722 
L 82.599755 61.984972 
L 82.81877 61.642129 
L 83.183795 62.688816 
L 83.2568 62.641908 
L 83.54882 62.718429 
L 83.69483 63.007301 
L 83.84084 62.291001 
L 84.205865 59.958444 
L 84.351875 59.522563 
L 84.7169 57.052702 
L 84.86291 57.665899 
L 85.30094 61.119718 
L 86.250005 72.259952 
L 86.396015 72.044041 
L 86.61503 71.222357 
L 86.76104 71.24944 
L 87.05306 72.592979 
L 87.19907 72.741044 
L 87.272075 72.685711 
L 87.34508 72.792457 
L 88.002125 76.836644 
L 88.294145 79.143592 
L 88.65917 80.247196 
L 88.732175 80.290295 
L 88.878185 79.646456 
L 89.68124 73.677426 
L 89.900255 72.650148 
L 90.192275 69.363108 
L 90.630305 63.830653 
L 90.70331 63.824925 
L 90.922325 65.460257 
L 91.28735 69.209593 
L 91.72538 73.961099 
L 91.798385 73.949281 
L 91.944395 73.475251 
L 92.236415 72.534421 
L 92.30942 72.436879 
L 92.45543 72.942802 
L 92.74745 74.697224 
L 93.33149 79.29922 
L 93.404495 79.218333 
L 93.550505 79.045605 
L 93.696515 79.224645 
L 94.06154 79.959186 
L 94.280555 80.365898 
L 94.426565 79.79402 
L 94.64558 78.50448 
L 94.718585 78.545549 
L 94.9376 79.20126 
L 95.010605 79.07138 
L 95.22962 77.090564 
L 95.594645 74.084778 
L 95.81366 73.54432 
L 95.95967 74.054776 
L 96.324695 76.138695 
L 96.3977 76.02814 
L 96.54371 75.221499 
L 97.12775 71.414216 
L 97.27376 71.424921 
L 97.56578 71.628709 
L 97.638785 71.581661 
L 97.784795 72.114751 
L 98.660855 79.307951 
L 98.87987 80.199788 
L 99.02588 80.204681 
L 99.098885 80.350411 
L 99.390905 82.578947 
L 99.828935 87.9491 
L 100.63199 102.761259 
L 100.851005 103.533254 
L 100.997015 102.824406 
L 101.21603 100.393751 
L 101.727065 90.488503 
L 102.09209 84.225484 
L 102.311105 82.74895 
L 102.38411 82.886171 
L 102.67613 86.595495 
L 103.479185 98.497575 
L 103.84421 101.476084 
L 103.917215 101.454785 
L 104.063225 100.368865 
L 104.501255 94.428975 
L 104.86628 88.107369 
L 105.30431 81.255594 
L 105.45032 80.342875 
L 105.523325 80.339956 
L 105.815345 80.985268 
L 105.88835 80.778256 
L 106.107365 80.595934 
L 106.18037 80.437915 
L 106.399385 80.771833 
L 106.76441 79.964219 
L 106.983425 79.826414 
L 107.20244 78.674789 
L 107.421455 76.214493 
L 107.49446 37.674806 
L 107.567465 38.086023 
L 107.64047 38.245127 
L 107.78648 37.880345 
L 107.93249 38.22013 
L 108.0785 39.289895 
L 108.297515 40.733812 
L 108.51653 41.611274 
L 108.735545 43.946667 
L 109.10057 47.244218 
L 109.68461 51.13083 
L 110.195645 58.424631 
L 110.56067 60.87767 
L 110.633675 60.691177 
L 111.29072 55.304842 
L 111.363725 55.357339 
L 111.509735 57.099805 
L 111.72875 59.003349 
L 111.801755 59.046976 
L 111.947765 58.183 
L 112.16678 56.154359 
L 112.677815 47.828636 
L 112.823825 47.019131 
L 113.261855 42.956843 
L 113.553875 40.408793 
L 113.991905 42.089948 
L 114.06491 41.979587 
L 114.137915 42.210791 
L 114.50294 46.881661 
L 114.79496 50.827137 
L 114.94097 51.630469 
L 115.159985 51.667701 
L 115.452005 50.251756 
L 115.744025 48.829416 
L 116.036045 47.216412 
L 116.10905 47.362225 
L 116.182055 47.18502 
L 116.25506 47.242772 
L 116.328065 47.163582 
L 116.474075 46.276861 
L 116.766095 44.714547 
L 117.13112 42.697194 
L 117.27713 42.830995 
L 117.56915 43.519601 
L 118.00718 42.157043 
L 118.226195 41.663076 
L 118.2992 41.793818 
L 118.73723 44.488042 
L 119.102255 43.62187 
L 119.540285 39.668135 
L 119.978315 35.861213 
L 120.270335 34.338717 
L 120.562355 32.015058 
L 120.854375 32.969597 
L 120.92738 32.746039 
L 121.000385 32.52112 
L 121.2194 31.225295 
L 121.292405 31.165235 
L 121.51142 29.126055 
L 121.80344 27.252986 
L 121.94945 27.269808 
L 122.09546 27.154137 
L 122.24147 28.070137 
L 122.898515 34.396414 
L 123.044525 35.272485 
L 123.482555 39.566199 
L 123.84758 40.040952 
L 124.066595 39.680397 
L 124.1396 39.52602 
L 124.358615 38.852541 
L 124.43162 38.936708 
L 124.650635 38.624229 
L 124.86965 38.705922 
L 125.088665 40.601069 
L 126.18374 54.116263 
L 126.47576 56.012355 
L 127.35182 59.821835 
L 127.49783 59.614266 
L 127.78985 57.726404 
L 128.300885 52.54102 
L 128.738915 47.908744 
L 129.10394 44.835029 
L 129.176945 44.521465 
L 129.322955 45.129546 
L 129.54197 45.753392 
L 129.760985 45.819125 
L 129.98 46.779113 
L 130.053005 46.696308 
L 130.199015 46.289373 
L 130.41803 44.788204 
L 130.71005 41.537673 
L 130.85606 40.617863 
L 131.00207 40.678257 
L 131.29409 42.265818 
L 131.73212 46.174925 
L 131.805125 46.10505 
L 132.02414 44.859192 
L 132.535175 35.990593 
L 132.9002 29.539052 
L 133.04621 28.28285 
L 133.119215 28.340129 
L 133.33823 27.729796 
L 133.77626 30.725851 
L 133.849265 30.554012 
L 134.06828 29.464171 
L 134.3603 27.807318 
L 134.65232 26.961416 
L 134.79833 27.481742 
L 135.017345 29.816301 
L 135.455375 34.498071 
L 136.039415 39.709954 
L 136.185425 39.482504 
L 136.477445 41.754334 
L 136.769465 44.48604 
L 136.915475 45.071043 
L 136.98848 44.897424 
L 137.2805 42.289564 
L 137.57252 39.005666 
L 137.645525 39.000772 
L 138.667595 46.095985 
L 138.813605 46.059393 
L 139.17863 49.081724 
L 139.61666 53.623575 
L 139.835675 54.643708 
L 139.90868 54.540966 
L 140.2007 53.701765 
L 141.22277 48.935717 
L 141.51479 47.115645 
L 141.733805 45.629268 
L 142.24484 41.840531 
L 142.53686 40.921 
L 142.609865 41.005279 
L 143.047895 42.855714 
L 143.26691 43.671002 
L 143.70494 41.901314 
L 143.85095 41.615945 
L 144.069965 40.315116 
L 144.507995 41.436405 
L 144.581 41.137467 
L 144.654005 41.263148 
L 144.800015 42.201448 
L 144.87302 42.035449 
L 145.238045 40.497187 
L 145.31105 40.634852 
L 145.45706 41.096314 
L 145.60307 41.003082 
L 145.676075 41.148756 
L 145.89509 40.271572 
L 146.0411 39.097508 
L 146.260115 37.339248 
L 146.698145 34.409176 
L 147.06317 39.183594 
L 147.428195 43.186796 
L 147.5012 43.32713 
L 147.574205 43.017793 
L 147.64721 43.27911 
L 147.720215 43.124372 
L 147.866225 41.779748 
L 147.93923 41.871006 
L 148.012235 41.997855 
L 148.158245 41.875233 
L 148.304255 42.917331 
L 148.37726 42.793318 
L 148.66928 40.707647 
L 149.472335 32.029572 
L 149.54534 32.120719 
L 149.69135 33.039027 
L 149.910365 35.161179 
L 150.056375 35.921913 
L 150.4214 38.325958 
L 150.640415 36.845142 
L 150.85943 35.284135 
L 150.932435 35.387461 
L 151.224455 37.456226 
L 151.73549 43.961793 
L 152.02751 47.013821 
L 152.46554 48.047772 
L 152.684555 47.626462 
L 152.90357 48.091177 
L 152.976575 48.204651 
L 153.04958 48.153239 
L 153.414605 46.868203 
L 153.77963 47.953706 
L 154.07165 49.660748 
L 154.36367 52.998144 
L 154.8017 58.364543 
L 154.94771 59.088601 
L 155.166725 59.515278 
L 155.312735 59.785938 
L 155.67776 58.015332 
L 156.334805 54.290159 
L 156.626825 51.808092 
L 156.69983 51.862507 
L 157.210865 55.551311 
L 157.356875 55.907612 
L 157.57589 53.905302 
L 157.794905 51.813291 
L 157.86791 51.869431 
L 158.086925 52.694534 
L 158.378945 54.059095 
L 158.816975 56.922294 
L 158.88998 56.887009 
L 159.03599 56.724846 
L 159.255005 57.502402 
L 159.401015 56.619435 
L 159.62003 54.916258 
L 159.693035 55.032402 
L 160.05806 56.793943 
L 160.131065 56.825002 
L 160.20407 57.008019 
L 161.22614 63.154226 
L 161.445155 65.076233 
L 161.81018 67.727498 
L 161.95619 68.265759 
L 162.613235 75.737571 
L 162.97826 80.564041 
L 163.051265 80.743554 
L 163.197275 79.91503 
L 163.927325 68.549098 
L 164.219345 64.50822 
L 164.58437 62.744399 
L 164.87639 62.263529 
L 165.0224 61.260442 
L 165.387425 58.380726 
L 165.60644 57.497536 
L 165.75245 57.033961 
L 166.04447 57.235163 
L 166.19048 56.985858 
L 166.4825 54.502983 
L 168.38063 27.453353 
L 168.453635 27.426771 
L 168.96467 23.266468 
L 169.037675 23.451625 
L 169.11068 23.219587 
L 169.25669 22.39123 
L 169.4027 22.846464 
L 169.475705 22.523391 
L 169.54871 22.573552 
L 169.69472 22.832478 
L 169.913735 21.223089 
L 169.98674 21.447703 
L 170.059745 21.170064 
L 170.205755 21.986881 
L 170.351765 23.279286 
L 170.497775 23.874159 
L 170.935805 27.588516 
L 171.081815 27.31135 
L 171.15482 27.415815 
L 171.30083 28.070471 
L 171.957875 40.952059 
L 172.17689 43.265068 
L 172.249895 43.246188 
L 172.61492 44.187937 
L 173.125955 46.67754 
L 173.271965 47.109861 
L 173.417975 46.416167 
L 173.49098 46.551886 
L 173.563985 46.67462 
L 173.63699 46.518019 
L 173.92901 44.367005 
L 174.440045 37.234032 
L 174.586055 36.520317 
L 174.80507 37.096644 
L 174.878075 36.84036 
L 175.024085 37.069728 
L 175.170095 36.415824 
L 175.2431 36.611965 
L 175.38911 36.595726 
L 176.192165 30.344219 
L 176.630195 29.712781 
L 176.776205 29.47813 
L 176.922215 30.026706 
L 177.506255 33.831042 
L 177.72527 34.732806 
L 177.798275 34.824425 
L 178.01729 34.093277 
L 178.090295 34.252381 
L 178.30931 34.461145 
L 178.45532 35.115633 
L 178.528325 35.061746 
L 178.74734 35.351675 
L 179.112365 32.958419 
L 179.47739 29.033295 
L 179.550395 29.130726 
L 179.842415 30.910063 
L 179.91542 30.958083 
L 180.134435 32.057434 
L 180.49946 34.498683 
L 180.718475 33.375364 
L 180.79148 33.394077 
L 181.302515 34.726967 
L 181.448525 34.883067 
L 181.52153 35.115689 
L 182.324585 43.25403 
L 182.39759 43.186545 
L 182.5436 43.233064 
L 182.68961 42.735399 
L 183.12764 45.187048 
L 183.200645 44.984484 
L 184.0037 38.708035 
L 184.514735 30.688091 
L 184.660745 31.061603 
L 184.73375 30.996928 
L 184.806755 30.655642 
L 185.60981 33.70486 
L 185.682815 33.434423 
L 185.75582 33.589745 
L 185.974835 33.866272 
L 186.04784 34.102063 
L 186.120845 34.06244 
L 186.9239 29.889764 
L 187.06991 31.065969 
L 187.36193 34.096502 
L 187.50794 34.282855 
L 187.65395 34.245151 
L 187.726955 34.243205 
L 187.872965 33.268062 
L 188.164985 30.96281 
L 188.23799 31.063077 
L 188.53001 32.441151 
L 188.749025 32.877588 
L 189.479075 36.327068 
L 189.625085 36.457171 
L 189.8441 35.958199 
L 189.917105 36.032357 
L 190.28213 37.055825 
L 190.647155 38.214096 
L 190.72016 38.036057 
L 190.793165 38.111326 
L 191.085185 39.724969 
L 191.231195 40.525993 
L 191.3042 40.3888 
L 191.45021 40.449695 
L 191.669225 41.202671 
L 191.815235 41.158015 
L 192.545285 36.765257 
L 192.691295 37.219183 
L 192.91031 38.15551 
L 192.983315 38.242152 
L 193.20233 38.748631 
L 193.34834 39.397531 
L 193.859375 42.636884 
L 194.297405 44.6068 
L 194.37041 44.556389 
L 194.51642 43.888887 
L 194.66243 44.017683 
L 194.95445 42.946388 
L 195.319475 38.530857 
L 195.757505 36.559495 
L 195.83051 36.655147 
L 195.97652 36.271374 
L 196.049525 36.334437 
L 196.195535 36.61747 
L 196.26854 36.546093 
L 196.487555 37.302962 
L 196.56056 37.119917 
L 196.633565 37.181145 
L 196.779575 36.943685 
L 196.925585 36.344947 
L 197.071595 36.232613 
L 198.02066 29.462225 
L 198.239675 30.541305 
L 199.115735 37.39878 
L 199.261745 37.771764 
L 199.407755 36.843279 
L 199.991795 33.76759 
L 200.137805 35.402003 
L 200.50283 44.247079 
L 200.94086 52.093739 
L 201.159875 53.739747 
L 201.37889 54.107115 
L 201.597905 53.193256 
L 201.743915 53.422597 
L 201.81692 53.180354 
L 201.96293 53.881278 
L 202.327955 56.275814 
L 202.54697 57.403331 
L 202.911995 59.537773 
L 203.058005 59.567581 
L 203.27702 58.93378 
L 203.56904 58.876778 
L 203.788055 59.186783 
L 203.86106 59.177607 
L 204.226085 60.009662 
L 204.518105 58.485637 
L 204.956135 55.692591 
L 204.956135 55.692591 
" clip-path="url(#pfe133dedb3)" style="fill: none; stroke: #000000; stroke-linecap: square"/>
   </g>
   <g id="patch_3">
    <path d="M 38.72375 138.0384 
L 38.72375 7.2 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 213.93575 138.0384 
L 213.93575 7.2 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 38.72375 138.0384 
L 213.93575 138.0384 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 38.72375 7.2 
L 213.93575 7.2 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pfe133dedb3">
   <rect x="38.72375" y="7.2" width="175.212" height="130.8384"/>
  </clipPath>
 </defs>
</svg>
